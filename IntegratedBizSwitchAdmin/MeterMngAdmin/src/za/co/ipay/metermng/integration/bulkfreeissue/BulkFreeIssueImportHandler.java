package za.co.ipay.metermng.integration.bulkfreeissue;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import za.co.ipay.metermng.fileimport.AbstractFileImportHandler;
import za.co.ipay.metermng.fileimport.FileImportItemDataConverter;
import za.co.ipay.metermng.fileimport.ImportItemDataConverter;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.ImportFile;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileItem;
import za.co.ipay.metermng.server.mybatis.service.AppSettingService;
import za.co.ipay.metermng.server.mybatis.service.ImportFileDataService;
import za.co.ipay.metermng.server.mybatis.service.MeterService;
import za.co.ipay.metermng.server.mybatis.service.TokenGenerationService;
import za.co.ipay.metermng.server.mybatis.service.UsagePointService;
import za.co.ipay.metermng.ipayxml.IpayXmlMessageService;
import za.co.ipay.ipayxml.metermng.StsEngTokenResMessage;
import za.co.ipay.metermng.datatypes.StsEngineeringTokenTypeE;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueImportRecord;

/**
 * Handler for bulk free issue import.
 */
public class BulkFreeIssueImportHandler extends AbstractFileImportHandler {

    private static final Logger logger = Logger.getLogger(BulkFreeIssueImportHandler.class);
    private static final String COMMA_DELIMITER = ",";

    private AppSettingService appSettingService;
    private MeterService meterService;
    private UsagePointService usagePointService;
    private TokenGenerationService tokenGenerationService;
    private IpayXmlMessageService ipayXmlMessageService;

    private int maxMeters;
    private int maxUnits;

    public BulkFreeIssueImportHandler() {
        super();
    }

    @Override
    public void init() {
        super.init();
        appSettingService = getBean(AppSettingService.class);
        meterService = getBean(MeterService.class);
        usagePointService = getBean(UsagePointService.class);
        tokenGenerationService = getBean(TokenGenerationService.class);
        ipayXmlMessageService = getBean(IpayXmlMessageService.class);

        // Get max meters and units from app settings
        try {
            AppSetting maxMetersAppSetting = appSettingService.getAppSettingByKey("bulk.free.issue.max.meters");
            if (maxMetersAppSetting != null) {
                maxMeters = Integer.parseInt(maxMetersAppSetting.getValue());
            } else {
                maxMeters = 0;
            }

            AppSetting maxUnitsAppSetting = appSettingService.getAppSettingByKey("bulk.free.issue.max.units");
            if (maxUnitsAppSetting != null) {
                maxUnits = Integer.parseInt(maxUnitsAppSetting.getValue());
            } else {
                maxUnits = 0;
            }
        } catch (NumberFormatException e) {
            logger.error("Error parsing app settings for bulk free issue", e);
            maxMeters = 0;
            maxUnits = 0;
        }
    }

    @Override
    public List<ImportFileItem> parseFile(ImportFile importFile, InputStream inputStream) throws IOException {
        List<ImportFileItem> importFileItems = new ArrayList<ImportFileItem>();

        BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
        String line;
        int lineNumber = 0;

        // Skip header line
        br.readLine();
        lineNumber++;

        while ((line = br.readLine()) != null) {
            lineNumber++;

            if (StringUtils.isBlank(line)) {
                continue;
            }

            String[] values = line.split(COMMA_DELIMITER);

            if (values.length < 5) {
                logger.warn("Line " + lineNumber + " has fewer than 5 values: " + line);
                continue;
            }

            String meterNumber = values[0].trim();
            String units = values[1].trim();
            String description = values[2].trim();
            String reference = values[3].trim();
            String reason = values[4].trim();

            // Create import record
            BulkFreeIssueImportRecord record = new BulkFreeIssueImportRecord(
                    meterNumber, units, description, reference, reason);

            // Create import file item
            ImportFileItem item = new ImportFileItem();
            item.setImportFileId(importFile.getId());
            item.setItemData(line);
            item.setUploadSuccessful(true);           // Use existing field - indicates successful upload
            item.setUploadDate(new Date());           // Use existing field - timestamp for upload
            item.setNumImportAttempts(0);             // Initialize import attempts
            item.setLastImportSuccessful(false);      // Not yet imported, so false
            item.setComment("NEW");

            // Add to list
            importFileItems.add(item);
        }

        return importFileItems;
    }

    @Override
    public ImportItemDataConverter getImportItemDataConverter() {
        return new FileImportItemDataConverter() {

            @Override
            public Object convertToObject(String itemData) {
                if (StringUtils.isBlank(itemData)) {
                    return null;
                }

                String[] values = itemData.split(COMMA_DELIMITER);

                if (values.length < 5) {
                    return null;
                }

                String meterNumber = values[0].trim();
                String units = values[1].trim();
                String description = values[2].trim();
                String reference = values[3].trim();
                String reason = values[4].trim();

                return new BulkFreeIssueImportRecord(meterNumber, units, description, reference, reason);
            }

            @Override
            public String convertToString(Object object) {
                if (!(object instanceof BulkFreeIssueImportRecord)) {
                    return null;
                }

                BulkFreeIssueImportRecord record = (BulkFreeIssueImportRecord) object;

                StringBuilder sb = new StringBuilder();
                sb.append(record.getMeterNumber()).append(COMMA_DELIMITER);
                sb.append(record.getUnits()).append(COMMA_DELIMITER);
                sb.append(record.getDescription()).append(COMMA_DELIMITER);
                sb.append(record.getReference()).append(COMMA_DELIMITER);
                sb.append(record.getReason());

                return sb.toString();
            }
        };
    }

    @Override
    public String processImportFileItem(ImportFileItemDto importFileItemDto, String userName, String ipAddress) {
        BulkFreeIssueImportRecord record = importFileItemDto.getBulkFreeIssueImportRecord();

        if (record == null) {
            return "Invalid record";
        }

        // Validate meter number
        if (StringUtils.isBlank(record.getMeterNumber())) {
            return "Meter number is required";
        }

        // Validate units
        if (StringUtils.isBlank(record.getUnits())) {
            return "Units is required";
        }

        int units;
        try {
            units = Integer.parseInt(record.getUnits());
        } catch (NumberFormatException e) {
            return "Units must be a valid number";
        }

        // Check if units exceed max units
        if (maxUnits > 0 && units > maxUnits) {
            return "Units exceed maximum allowed (" + maxUnits + ")";
        }

        // Check if meter exists
        if (meterService.getMeterByNumber(record.getMeterNumber()) == null) {
            return "Meter not found: " + record.getMeterNumber();
        }

        // Generate free issue token
        try {
            // Use TokenGenerationService to send proper StsEngTokenReqMessage
            StsEngTokenResMessage response = tokenGenerationService.sendStsEngTokenReqMessage(
                    record.getDescription() != null ? record.getDescription() : "Bulk Free Issue",
                    record.getMeterNumber(),
                    record.getReference() != null ? record.getReference() : "BULK_FREE_ISSUE",
                    StsEngineeringTokenTypeE.FREE_ISSUE,
                    Integer.parseInt(record.getUnits()),
                    null // specialActionReasonsLog
            );

            // Process response
            String result = processFreeIssueTokenResponse(response, importFileItemDto, userName, ipAddress);
            return result;

        } catch (Exception e) {
            logger.error("Error generating free issue token", e);
            return "Error generating free issue token: " + e.getMessage();
        }
    } // Proper closing brace for processImportFileItem method

    private String processFreeIssueTokenResponse(StsEngTokenResMessage response, ImportFileItemDto importFileItemDto, String userName, String ipAddress) {
        if (response != null && response.isSuccessful()) {
            // Extract token codes
            String[] tokenCodes = response.getTokenCodes();
            String tokenCode = (tokenCodes != null && tokenCodes.length > 0) ? tokenCodes[0] : "NO_TOKEN";

            // Record audit information
            recordAuditInfo(importFileItemDto, userName, ipAddress, tokenCode);

            return "Success: Token generated - " + tokenCode;
        } else {
            String errorMsg = (response != null) ? response.getRes() : "No response received";
            return "Error: Failed to generate token - " + errorMsg;
        }
    }

    private void recordAuditInfo(ImportFileItemDto importFileItemDto, String userName, String ipAddress, String tokenCode) {
        // In a real implementation, this would record audit information in the database
        // For this implementation, we'll just log the information

        BulkFreeIssueImportRecord record = importFileItemDto.getBulkFreeIssueImportRecord();

        logger.info("Bulk Free Issue Token Generated:");
        logger.info("  User: " + userName);
        logger.info("  Time: " + new Date());
        logger.info("  Meter: " + record.getMeterNumber());
        logger.info("  Units: " + record.getUnits());
        logger.info("  IP Address: " + ipAddress);
        logger.info("  Token Code: " + tokenCode);
        logger.info("  Bulk Ref: " + importFileItemDto.getImportFileItem().getImportFileId());
    }

    @Override
    public boolean validateImportFile(ImportFile importFile, List<ImportFileItem> importFileItems) {
        // Check if number of meters exceeds maximum
        if (maxMeters > 0 && importFileItems.size() > maxMeters) {
            importFile.setStopImport(true);  // Use existing stopImport field
            importFile.setLastStopImport(new Date());  // Set the stop time
            // You might need to log the error message separately or store it elsewhere
            logger.error("Number of meters (" + importFileItems.size() + ") exceeds maximum allowed (" + maxMeters + ")");
            return false;
        }
        return true;
    }

    @Override
    public boolean hasActionParams() {
        return true;
    }

    @Override
    public boolean hasInputFile() {
        return true;
    }
}
