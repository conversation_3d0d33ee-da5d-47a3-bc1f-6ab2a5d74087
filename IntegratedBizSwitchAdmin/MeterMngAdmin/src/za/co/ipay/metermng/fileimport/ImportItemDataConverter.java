package za.co.ipay.metermng.fileimport;

/**
 * Interface for converting between import item data and objects.
 */
public interface ImportItemDataConverter {

    /**
     * Convert a string representation to an object.
     *
     * @param itemData the string representation
     * @return the object
     */
    Object convertToObject(String itemData);

    /**
     * Convert an object to a string representation.
     *
     * @param object the object
     * @return the string representation
     */
    String convertToString(Object object);
}