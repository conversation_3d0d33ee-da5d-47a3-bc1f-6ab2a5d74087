package za.co.ipay.metermng.fileimport;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import za.co.ipay.metermng.mybatis.generated.model.ImportFile;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileItem;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;

/**
 * Interface for file import handlers.
 */
public interface FileImportHandler {

    /**
     * Initialize the handler.
     */
    void init();

    /**
     * Parse the input file and create import file items.
     *
     * @param importFile the import file
     * @param inputStream the input stream
     * @return the list of import file items
     * @throws IOException if an I/O error occurs
     */
    List<ImportFileItem> parseFile(ImportFile importFile, InputStream inputStream) throws IOException;

    /**
     * Process an import file item.
     *
     * @param importFileItemDto the import file item DTO
     * @param userName the user name
     * @param ipAddress the IP address
     * @return the result message
     */
    String processImportFileItem(ImportFileItemDto importFileItemDto, String userName, String ipAddress);

    /**
     * Validate the import file.
     *
     * @param importFile the import file
     * @param importFileItems the import file items
     * @return true if the import file is valid, false otherwise
     */
    boolean validateImportFile(ImportFile importFile, List<ImportFileItem> importFileItems);

    /**
     * Check if the handler has action parameters.
     *
     * @return true if the handler has action parameters, false otherwise
     */
    boolean hasActionParams();

    /**
     * Check if the handler has an input file.
     *
     * @return true if the handler has an input file, false otherwise
     */
    boolean hasInputFile();

    /**
     * Get the import item data converter.
     *
     * @return the import item data converter
     */
    ImportItemDataConverter getImportItemDataConverter();

    /**
     * Get the import parameter record converter.
     *
     * @return the import parameter record converter
     */
    ImportParamRecordConverter getImportParamRecordConverter();

    /**
     * Check if failed items can be exported.
     *
     * @return true if failed items can be exported, false otherwise
     */
    boolean allowExportFailedItems();
}
