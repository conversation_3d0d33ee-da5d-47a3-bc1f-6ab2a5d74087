package za.co.ipay.metermng.fileimport;

/**
 * Interface for converting between import parameter records and objects.
 */
public interface ImportParamRecordConverter {

    /**
     * Convert a string representation to an object.
     *
     * @param paramData the string representation
     * @return the object
     */
    Object convertToObject(String paramData);

    /**
     * Convert an object to a string representation.
     *
     * @param object the object
     * @return the string representation
     */
    String convertToString(Object object);
}