package za.co.ipay.metermng.fileimport;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import za.co.ipay.metermng.mybatis.generated.model.ImportFile;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileItem;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;

/**
 * Abstract implementation of the FileImportHandler interface.
 */
public abstract class AbstractFileImportHandler implements FileImportHandler, ApplicationContextAware {

    private ApplicationContext applicationContext;

    /**
     * Default constructor.
     */
    public AbstractFileImportHandler() {
        // Default constructor
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * Get a bean from the application context.
     *
     * @param <T> the type of the bean
     * @param beanClass the class of the bean
     * @return the bean
     */
    protected <T> T getBean(Class<T> beanClass) {
        return applicationContext.getBean(beanClass);
    }

    @Override
    public void init() {
        // Default implementation, can be overridden by subclasses
    }

    @Override
    public abstract ImportItemDataConverter getImportItemDataConverter();

    @Override
    public ImportParamRecordConverter getImportParamRecordConverter() {
        // Default implementation returns null, can be overridden by subclasses
        return null;
    }

    @Override
    public abstract List<ImportFileItem> parseFile(ImportFile importFile, InputStream inputStream) throws IOException;

    @Override
    public abstract String processImportFileItem(ImportFileItemDto importFileItemDto, String userName, String ipAddress);

    @Override
    public abstract boolean validateImportFile(ImportFile importFile, List<ImportFileItem> importFileItems);

    @Override
    public boolean hasActionParams() {
        return false;
    }

    @Override
    public boolean hasInputFile() {
        return true;
    }

    @Override
    public boolean allowExportFailedItems() {
        // Default implementation returns false, can be overridden by subclasses
        return false;
    }
}
