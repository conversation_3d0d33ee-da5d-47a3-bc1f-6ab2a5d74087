package za.co.ipay.metermng.client.view.component.importfile;

import java.util.List;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueImportRecord;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueParseResult;

/**
 * Dialogue box for editing bulk free issue import records.
 */
public class BulkFreeIssueImportDialogueBox extends ImportFileItemBaseDialogueBox {

    private BulkFreeIssueImportRecord recordIn;

    public BulkFreeIssueImportDialogueBox(ClientFactory clientFactory, ImportFileItemView importFileItemView) {
        super(clientFactory, importFileItemView);
    }

    @Override
    protected List<ImportRecordField> createDataList(ImportFileItemDto itemDto) {
        BulkFreeIssueImportRecord record = recordIn = itemDto.getBulkFreeIssueImportRecord();
        dataList.clear();
        dataList.add(new ImportRecordField("Meter Number", record.getMeterNumber()));
        dataList.add(new ImportRecordField("Units", record.getUnits()));
        dataList.add(new ImportRecordField("Description", record.getDescription()));
        dataList.add(new ImportRecordField("Reference", record.getReference()));
        dataList.add(new ImportRecordField("Reason", record.getReason()));
        return dataList;
    }

    @Override
    protected void checkDirtyData() {
        isDirtyData = false;
        BulkFreeIssueParseResult result = createRecordFromList();
        if (result.getErrorMsg() != null && !result.getErrorMsg().isEmpty()) {
            isDirtyData = true;
            return;
        }
        BulkFreeIssueImportRecord chgRec = result.getBulkFreeIssueImportRecord();
        if (!recordIn.getMeterNumber().equals(chgRec.getMeterNumber())) {
            isDirtyData = true;
            return;
        }
        if (!recordIn.getUnits().equals(chgRec.getUnits())) {
            isDirtyData = true;
            return;
        }
        if (!recordIn.getDescription().equals(chgRec.getDescription())) {
            isDirtyData = true;
            return;
        }
        if (!recordIn.getReference().equals(chgRec.getReference())) {
            isDirtyData = true;
            return;
        }
        if (!recordIn.getReason().equals(chgRec.getReason())) {
            isDirtyData = true;
            return;
        }
    }

    private BulkFreeIssueParseResult createRecordFromList() {
        StringBuilder errorBuilder = new StringBuilder();

        BulkFreeIssueImportRecord chgRec = new BulkFreeIssueImportRecord();
        for (ImportRecordField field : dataProvider.getList()) {
            if (field.getFieldname().equals("Meter Number")) {
                chgRec.setMeterNumber(field.getFieldValue());
            }
            if (field.getFieldname().equals("Units")) {
                chgRec.setUnits(field.getFieldValue());
            }
            if (field.getFieldname().equals("Description")) {
                chgRec.setDescription(field.getFieldValue());
            }
            if (field.getFieldname().equals("Reference")) {
                chgRec.setReference(field.getFieldValue());
            }
            if (field.getFieldname().equals("Reason")) {
                chgRec.setReason(field.getFieldValue());
            }
        }

        // Validate the record
        if (chgRec.getMeterNumber() == null || chgRec.getMeterNumber().trim().isEmpty()) {
            errorBuilder.append("Meter Number is required. ");
        }
        if (chgRec.getUnits() == null || chgRec.getUnits().trim().isEmpty()) {
            errorBuilder.append("Units is required. ");
        } else {
            try {
                Integer.parseInt(chgRec.getUnits());
            } catch (NumberFormatException e) {
                errorBuilder.append("Units must be a valid number. ");
            }
        }

        String errors = null;
        if (errorBuilder.length() != 0) {
            errorBuilder.insert(0, "Errors: ");
            errors = errorBuilder.toString();
        }
        return (new BulkFreeIssueParseResult(chgRec, errors));
    }

    @Override
    protected void updateParentRow() {
        BulkFreeIssueParseResult result = createRecordFromList();
        importFileItemDto.setGenericImportRecord(result.getBulkFreeIssueImportRecord());
        // eventual update in DefaultImportService is selective, so null won't update
        importFileItemDto.getImportFileItem().setComment("");
        importFileItemDto.setImportFileItemImportComment(result.getErrorMsg());
    }

    @Override
    protected void displayUpdateMessage() {
        BulkFreeIssueParseResult result = createRecordFromList();
        BulkFreeIssueImportRecord record = result.getBulkFreeIssueImportRecord();
        Dialogs.displayInformationMessage(MessagesUtil.getInstance()
                .getMessage("import.edit.bulk.free.issue.item.update.success",
                        new String[] { record.getMeterNumber() }),
                MediaResourceUtil.getInstance().getInformationIcon());
    }

    @Override
    protected void prepareImportFileItem(ImportFileItemDto importFileItemDto) {
        BulkFreeIssueParseResult result = createRecordFromList();
        importFileItemDto.setGenericImportRecord(result.getBulkFreeIssueImportRecord());
        // eventual update in DefaultImportService is selective, so null won't update
        importFileItemDto.getImportFileItem().setComment("");
        importFileItemDto.setImportFileItemImportComment(result.getErrorMsg());
    }
}