package za.co.ipay.metermng.client.view.component.importfile;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DisclosurePanel;
import com.google.gwt.user.client.ui.FileUpload;
import com.google.gwt.user.client.ui.FormPanel;
import com.google.gwt.user.client.ui.FormPanel.SubmitCompleteEvent;
import com.google.gwt.user.client.ui.FormPanel.SubmitEvent;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Hidden;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.ProvidesResize;
import com.google.gwt.user.client.ui.RequiresResize;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.dataexport.GetRequestBuilder;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.resource.StyleNames;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.WaitingDialog;
import za.co.ipay.gwt.common.client.workspace.WaitingDialog.WaitingDialogUtil;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.metermng.client.MeterMngAdmin;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.login.LoginView.LoginViewUtil;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileType;
import za.co.ipay.metermng.shared.MeterMngStatics;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.logging.Logger;

public class FileImportUploadPanel extends BaseComponent implements ProvidesResize, RequiresResize {

    @UiField DisclosurePanel fileUploadDisclosurePanel;
    private Boolean disclosureOpen = null;

    @UiField VerticalPanel uploadFilePanel;
    @UiField HTML uploadFileDescription;
    @UiField FormPanel uploadFileFormPanel;

    @UiField FormElement fileTypeElement;
    @UiField IpayListBox lstbxFileType;

    @UiField Hidden fileTypeName;
    @UiField Hidden enableAccessGroups;
    @UiField FormElement uploadFileElement;
    @UiField FileUpload uploadFile;
    @UiField Button btnUploadFile;
    @UiField Button btnDownloadTemplate;

    private String filenameForMessage;
    private String selectedTypeClass;
    private ImportFileView parent;

    private static Logger logger = Logger.getLogger(FileImportUploadPanel.class.getName());

    private static FileImportUploadPanelUiBinder uiBinder = GWT.create(FileImportUploadPanelUiBinder.class);

    interface FileImportUploadPanelUiBinder extends UiBinder<Widget, FileImportUploadPanel> {
    }

    public FileImportUploadPanel(ClientFactory clientFactory, ImportFileView parent) {
        this.clientFactory = clientFactory;
        this.parent = parent;
        initWidget(uiBinder.createAndBindUi(this));
        initHeaders();
        populateFileTypeLookupListBox();
        initMapping();
    }


    private void initHeaders() {
        //initialise Headers
        String descrip = MessagesUtil.getInstance().getMessage("import.file.explanation");
        if (descrip != null) {
            uploadFileDescription.setText(descrip);
            uploadFileDescription.setVisible(true);
        } else {
            uploadFileDescription.setVisible(false);
        }
    }

    public void populateFileTypeLookupListBox() {
        ClientCallback<List<ImportFileType>> asyncCallback = new ClientCallback<List<ImportFileType>>() {
            @Override
            public void onSuccess(List<ImportFileType> result) {
                List<LookupListItem> fileTypeLookupItemList = new ArrayList<LookupListItem>();
                LookupListItem lli = null;
                HashMap<String, Object> extraInfoMap = null;

                if (result != null) {
                    for (ImportFileType importFileType : result) {
                        lli = new LookupListItem(importFileType.getTypeClass(), importFileType.getName());
                        extraInfoMap = new HashMap<>();
                        String hasActionParams = importFileType.isHasActionParams() ? "true" : "false";
                        extraInfoMap.put("hasActionParams", hasActionParams);
                        extraInfoMap.put("hasInputFile", importFileType.getHasInputFile());
                        lli.setExtraInfoMap(extraInfoMap);

                        fileTypeLookupItemList.add(lli);
                    }
                    lstbxFileType.clear();
                    lstbxFileType.setLookupItemsWithEmptyFirst(fileTypeLookupItemList);
                }
                clearFileTypeListBox();
            }
        };
        clientFactory.getImportFileDataRpc().getActiveImportFileTypes(clientFactory.isEnableAccessGroups(), asyncCallback);
    }

    private void clearFileTypeListBox() {
        if (lstbxFileType.getItemCount() == 2) {
            lstbxFileType.setSelectedIndex(1);
        } else {
            lstbxFileType.setSelectedIndex(0);
        }
    }

    private void initMapping() {
        uploadFileFormPanel.setAction(GWT.getModuleBaseURL()+ "secure/importfileservlet.do");
        uploadFileFormPanel.setEncoding(FormPanel.ENCODING_MULTIPART);
        uploadFileFormPanel.setMethod(FormPanel.METHOD_POST);
        uploadFile.setName("file");
    }

    @UiHandler("lstbxFileType")
    void handleFileTypeChange(ChangeEvent event) {
        LookupListItem selectedFileType = lstbxFileType.getItem(lstbxFileType.getSelectedIndex());
        if(selectedFileType.getValue().equals(MeterMngStatics.FILETYPE_REGISTER_READING_IMPORT)
                || selectedFileType.getValue().equals(MeterMngStatics.FILETYPE_PRICING_STRUCTURE_CHANGE_IMPORT)
                || selectedFileType.getValue().equals(MeterMngStatics.FILETYPE_IPAY_DEBT_IMPORT)
                || selectedFileType.getValue().equals(MeterMngStatics.FILETYPE_BULK_MDC_IMPORT)) {
        	btnDownloadTemplate.setVisible(true);
        } else {
        	btnDownloadTemplate.setVisible(false);
        }
        String hasInputFile = String.valueOf(selectedFileType.getExtraInfoMap().get("hasInputFile")).toLowerCase();
        if (hasInputFile.equals("n")) {
            uploadFileElement.setVisible(false);
            btnUploadFile.setVisible(false);
            selectedTypeClass = selectedFileType.getValue();
            String message = MessagesUtil.getInstance().getMessage("import.file.parameters.needed.no.data");  //No filename specified, please confirm and submit parameters for bulk changes
            parent.loadActionParams(selectedTypeClass, null, message, true, false);           //no input file to upload
        } else {
            uploadFileElement.setVisible(true);
            btnUploadFile.setVisible(true);
        }
    }

    @UiHandler("btnDownloadTemplate")
    void handleDownloadImportTemplate(ClickEvent clickEvent) {
        LookupListItem selectedFileType = lstbxFileType.getItem(lstbxFileType.getSelectedIndex());
        String encodedUrl = new GetRequestBuilder().withBaseUrl(GWT.getHostPageBaseURL())
                .withTargetUrl("templateexport")
                .addParam("import-type", selectedFileType.getValue())
                .toEncodedUrl();
        if (encodedUrl != null) {
            Window.Location.assign(encodedUrl);
        }
    }

    @UiHandler("btnUploadFile")
    void handleUploadFileButton(ClickEvent clickEvent) {
        fileTypeElement.clearErrorMsg();
        LookupListItem selectedFileType = lstbxFileType.getItem(lstbxFileType.getSelectedIndex());
        String fileTypeValue = selectedFileType.getText();
        if (fileTypeValue == null || fileTypeValue.isEmpty()) {
            fileTypeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("import.upload.filetype.none"));
            clear();
            return;
        }
        selectedTypeClass = selectedFileType.getValue();
        fileTypeName.setValue(fileTypeValue);
        enableAccessGroups.setValue(String.valueOf(clientFactory.isEnableAccessGroups()));
        uploadFileElement.clearErrorMsg();
        //Validate a filename WAS selected when needed; if not needed go straight to action params
        String hasInputFile = String.valueOf(selectedFileType.getExtraInfoMap().get("hasInputFile")).toLowerCase();                ;
        if (uploadFile.getFilename() == null || uploadFile.getFilename().isEmpty()) {
            boolean hasActionParams = selectedFileType.getExtraInfoMap().get("hasActionParams").equals("true") ? true : false;
            if (hasInputFile.equals("y")) {            //must have file with input data
                uploadFileElement.showErrorMsg(MessagesUtil.getInstance().getMessage("import.upload.file.none"));
                clear();
            } else if (hasActionParams) {              //for no data or both, if have no filename go straight to action params
                String message = MessagesUtil.getInstance().getMessage("import.file.parameters.needed.no.data");  //No filename specified, please confirm and submit parameters for bulk changes
                parent.loadActionParams(selectedTypeClass, null, message, true, false);   //hasInputFile must be Both, but no filename entered, so inputFileUploaded = "n"
            } else {
                //if have no filename and no action params : error bad settings combo :no action Params and input data is N or Both (maybe)
                uploadFileElement.showErrorMsg(MessagesUtil.getInstance().getMessage("import.upload.file.settings.conflict"));
                clear();
            }
            return;
        //} else if (hasInputFile.equals("n")) { }      don't need to handle this - changeHandler disables the fileChooser
        }
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                parent.submitFileUploadPreProcess();
                submitUpload();
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void submitUpload() {
        WaitingDialogUtil.getInstance(MediaResourceUtil.getInstance().getWaitIcon());
        uploadFile.setEnabled(true);         //fileUpload must be enabled for the submit process to access the data!!
        String uploadUrl = GWT.getModuleBaseURL()+"secure/importfileservlet.do";
        logger.info("uploadUrl=" + uploadUrl);
        filenameForMessage = uploadFile.getFilename().replaceAll("/", "\\/");
        filenameForMessage = filenameForMessage.substring(filenameForMessage.lastIndexOf("\\") + 1);
        uploadFileFormPanel.setAction(uploadUrl);
        uploadFileFormPanel.submit();
        uploadFile.setEnabled(false);
    }

    @UiHandler("uploadFileFormPanel")
    void handleProcessTransFormSubmit(SubmitEvent submitEvent) {
        logger.info("****FileUpload selection: " + uploadFileFormPanel.getAction() + " : " + uploadFile.getFilename());
    }

    @UiHandler("uploadFileFormPanel")
    void handleProcessTransFormSubmitComplete(SubmitCompleteEvent submitCompleteEvent) {
        WaitingDialog waiting = WaitingDialogUtil.getCurrentInstance();
        if (waiting != null) {
            waiting.hide();
        }
        uploadFile.setEnabled(true);
        String result = submitCompleteEvent.getResults();
        if (result == null || result.contains("<div id=\"main\">")) {
            sessionTimeout();
            return;
        }
        logger.info("result=" + result);
        result = result.replace("<pre style=\"word-wrap: break-word; white-space: pre-wrap;\">", "").replace("</pre>", "").replace("&lt;", "<").replace("&gt;", ">").replace("&amp;", "").replace("&nbsp;","");

        /*
         * Cannot use GWT RPC calls when doing a File Upload
         * result will come back either with:   "success"
         *                                or:   "EXCEPTION: plus the messages.properties key to error message
         *
         *                                or:   "EXCEPTION: ServiceException:" plus the messages.properties key to error message
         *                                or if lowercase "exception" its something like MaxUploadSizeExceededException
         */

        if (result.toLowerCase().contains("exception")) {
            //this is messages.properties message
            String[] errMessages = result.split(";");
            String error = errMessages[0].substring(result.indexOf(':') + 1, result.length()).trim();  //Strip off "EXCEPTION"
            String errorMsg = MessagesUtil.getInstance().getMessage(error);
            if (errMessages.length > 1) {
                String str = errMessages[1];
                errorMsg = str.substring(str.indexOf(':') + 1).trim();  //Strip off java class name
            }
            Dialogs.centreErrorMessage(
                    errorMsg,
                    MediaResourceUtil.getInstance().getErrorIcon(),
                    MessagesUtil.getInstance().getMessage("button.close"));
            parent.submitFileUploadPostProcess(false);
        } else {
            logger.info("Successful upload of file: " + filenameForMessage);
            clearAll();
            parent.submitFileUploadPostProcess(true);
            String message = MessagesUtil.getInstance().getMessage("import.upload.completed");
            //from server get: result = "success;" + numFailedUpload + ";hasActionParams;" + hasActionParams
            String[] successArr = result.split(";");
            if (successArr.length > 1 && successArr[1] != null && !successArr[1].equals("0")) {
                message += MessagesUtil.getInstance().getMessage("import.items.unsuccessful.uploads.reminder", new String[] {successArr[1]});
            }

            if (successArr.length > 3 && successArr[3] != null && successArr[3].equals("true")) {
                message += MessagesUtil.getInstance().getMessage("import.file.parameters.needed");
                parent.loadActionParams(selectedTypeClass, filenameForMessage, message, true, true);      //would only come here if went to backend to upload a file so inputFileUploaded = true
            } else {
                Dialogs.centreMessage(message, new Image(MediaResourceUtil.getInstance().getInformationIcon()),
                        StyleNames.POPUP_MESSAGE,
                        MessagesUtil.getInstance().getMessage("button.close"), null,
                        false, true);
            }
        }
    }

    private void sessionTimeout() {
        logger.info("Allowing user to login again: from NoCurrentUserException ex Bulk Upload");
        clearAll();
        LoginViewUtil.getInstance(MeterMngAdmin.getClientFactory()).displayLoginView();
    }

    public void clearAll() {
        clear();
        clearFileTypeListBox();
    }

    private void clear() {
        btnUploadFile.setEnabled(true);
        btnUploadFile.setVisible(true);

        uploadFileElement.setVisible(true);
        uploadFile.getElement().setPropertyString("value", "");    //clear filename
        uploadFile.setEnabled(true);
    }

    public void setOpen(boolean isOpen) {
        if (disclosureOpen == null) {
            fileUploadDisclosurePanel.setOpen(isOpen);
        } else {
            fileUploadDisclosurePanel.setOpen(disclosureOpen);
        }
    }

    @Override
    public void onResize() {
        new Timer() {
            @Override
            public void run() {
                fileUploadDisclosurePanel.setWidth("100%");
            }
        }.schedule(100);

    }

}
