package za.co.ipay.metermng.shared;

import java.math.BigDecimal;

import za.co.ipay.metermng.datatypes.ServiceResourceE;

public class MeterMngStatics {

    /** The name of the session attribute for the current user. */
    public static final String METER_MNG_USER_ATTR = "meterMngUser";    
    /** The group type used for access contol. */
    public static final String ACCESS_CONTROL_GROUP_TYPE = "AccessControlGroupType";
    /** A group type. */
    public static final String GROUP_TYPE = "GroupType";
    /** The user's current group. */
    public static final String USER_CURRENT_GROUP = "UserCurrentGroup";
    /** The name of the groups. */
    public static final String GROUPS = "groups";
    public static final String GROUPSEDITEDXTREEPOPUP = "groupsX";

    /** Access Permissions */
    public static final String ACCESS_PERMISSION_UP_PRICING_STRUCT ="mm_up_pricing_struct_change";
    public static final String ACCESS_PERMISSION_MM_PRICING_STRUCT_ADMIN = "mm_pricing_struct_admin";
    public static final String EXPORT_PERMISSION_MM_PRICING_STRUCT = "mm_pricing_struct_export";             //Allow export all Pricing Structure Current tariff info to JSON file
    public static final String ACCESS_PERMISSION_MM_MDC_ADMIN = "mm_mdc_admin";                               
    public static final String ACCESS_PERMISSION_MM_AUX_TYPE_ADMIN = "mm_aux_type_admin";                    // Aux Type
    public static final String ACCESS_PERMISSION_MM_AUX_SCHEDULE_ADMIN = "mm_aux_schedule_admin";            // Aux Charge Schedule
    public static final String ACCESS_PERMISSION_MM_METER_STORE_ADMIN = "mm_meter_store_admin";              // DeviceStores
    public static final String ACCESS_PERMISSION_MM_GEN_STS_NON_METER_SPEC = "mm_gen_sts_non_meter_spec";   // Displaytokens
    public static final String ACCESS_PERMISSION_MM_GROUP_TYPE_ADMIN = "mm_group_type_admin";                // Group Types and Hierarchies
    public static final String ACCESS_PERMISSION_MM_GROUP_ADMIN = "mm_group_admin";                          // Usage Point Groups
    public static final String ACCESS_PERMISSION_MM_LOCATION_GROUP_ADMIN = "mm_location_group_admin";       // Location Groups
    public static final String ACCESS_PERMISSION_MM_ACCESS_GROUP_ADMIN = "mm_access_group_admin";           // Access Groups
    public static final String ACCESS_PERMISSION_MM_USER_GROUP_ADMIN = "mm_user_group_admin";               // User's Access Group
    public static final String APPSETTING_PERMISSION = "mm_app_settings_admin";                               // Application settings
    public static final String ACCESS_PERMISSION_MM_CUST_ACC_ADJ = "mm_cust_acc_adj";                        // Permission to adjust Customer Account Balance
    public static final String GLOBAL_NDP_PERMISSION = "mm_ndp_admin";                                         // Permission to set Global NDP times
    public static final String ACCESS_PERMISSION_MM_BILLING_DET_ADMIN = "mm_billing_det_admin";              // Permission to add / deactivate biling determinants
    public static final String ACCESS_PERMISSION_MM_BLOCKING_TYPE_ADMIN = "mm_blocking_type_admin";
    public static final String ACCESS_PERMISSION_MM_UP_UNITS_ACC_ADJ = "mm_up_units_acc_adj";

    public static final String ACCESS_PERMISSION_MM_NEW_METER = "mm_new_meter";                                           //Allow adding of new meters
    public static final String ACCESS_PERMISSION_MM_METER_MODEL_ADMIN = "mm_meter_model_admin";                          //Manage meter models
    public static final String ACCESS_PERMISSION_MM_METER_MANUFACTURER_ADMIN = "mm_meter_manufacturer_admin";           //Manage meter manufacturers
    public static final String ACCESS_PERMISSION_MM_ENERGY_BALANCING_METER_ADMIN = "mm_energy_balancing_meter_admin";   //Manage energy balancing meters
    public static final String ACCESS_PERMISSION_MM_SUPPLY_GROUP_ADMIN = "mm_supply_group_admin";                        //Manage supply groups
    public static final String ACCESS_PERMISSION_MM_NEW_CUSTOMER = "mm_new_customer";                                     //Allow adding of new customers
    public static final String ACCESS_PERMISSION_MM_ENERGY_BALANCING = "mm_energy_balancing";                            //Allow accessing energy balancing
    public static final String ACCESS_PERMISSION_MM_TASK_SCHEDULE_ADMIN = "mm_task_schedule_admin";                      //Manage task schedules
    public static final String ACCESS_PERMISSION_MM_PS_CALENDAR_ADMIN = "mm_ps_calendar_admin";                          //Manage calendars for time of use pricing
    public static final String ACCESS_PERMISSION_MM_METER_READINGS = "mm_meter_readings";                                //Allow accessing meter readings
    public static final String ACCESS_PERMISSION_MM_METER_READINGS_VIEW = "mm_meter_readings_view";                      //Allow accessing meter readings from meter information only

    public static final String ACCESS_PERMISSION_MM_MDC_DISCONNECT = "mm_mdc_disconnect";
    public static final String ACCESS_PERMISSION_MM_MDC_DISCONNECT_ENABLE = "mm_mdc_disconnect_enable";
    public static final String ACCESS_PERMISSION_MM_MDC_CONNECT = "mm_mdc_connect";
    public static final String ACCESS_PERMISSION_MM_MDC_PANDISPLAY = "mm_mdc_pandisplay";
    public static final String ACCESS_PERMISSION_MM_MDC_CONTROLREQ_OVERRIDE = "mm_mdc_controlreq_override";
    public static final String ACCESS_PERMISSION_MM_MDC_TRANS_VIEW = "mm_mdc_transaction_view";
    public static final String ACCESS_PERMISSION_MM_MDC_GEN_POWER_LIMIT = "mm_gen_power_limit";

    public static final String ACCESS_PERMISSION_MM_TRANS_UPLOAD = "mm_trans_upload";
    public static final String ACCESS_PERMISSION_MM_GENERATE_METERCUSTUP_UPLOAD_TEMPLATE = "mm_generate_metercustUp_upload_template";
    public static final String ACCESS_PERMISSION_MM_METADATA_UPLOAD = "mm_metadata_upload";
    public static final String ACCESS_PERMISSION_MM_AUX_TRANS_UPLOAD = "mm_aux_trans_upload";
    public static final String ACCESS_PERMISSION_MM_AUX_ACCOUNT_UPLOAD = "mm_aux_account_upload";
    public static final String ACCESS_PERMISSION_MM_SPECIAL_ACTIONS = "mm_special_actions";
    public static final String ACCESS_PERMISSION_MM_FILE_IMPORT = "mm_file_import";

    public static final String ACCESS_PERMISSION_MM_ONLINE_BULK = "mm_meter_online_bulk_admin";           //Allow access to meter online bulk search / capture by UP groups 

    public static final String ACCESS_PERMISSION_MM_OUTSTANDING_CHARGE_VIEW = "mm_outstanding_charge_view";
    public static final String ACCESS_PERMISSION_MM_OUTSTANDING_CHARGE_WRITEOFF = "mm_outstanding_charge_writeoff";

    public static final String ACCESS_PERMISSION_MM_DASHBOARD_VIEW = "mm_dashboard_view";
    public static final String ACCESS_PERMISSION_MM_ADMIN_DASHBOARD_VIEW = "mm_admin_dashboard_view";
    public static final String ACCESS_PERMISSION_MM_CONFIGURE_USER_INTERFACE = "mm_configure_user_interface";

    /** Edit Permissions */
    public static final String EDIT_PERMISSION_MM_METER_EDIT = "mm_meter_edit";
    public static final String EDIT_PERMISSION_MM_METER_ADD = "mm_meter_add";
    public static final String EDIT_PERMISSION_MM_CUSTOMER_EDIT = "mm_customer_edit";
    public static final String EDIT_PERMISSION_MM_USAGE_POINT_EDIT = "mm_usage_point_edit";
    public static final String EDIT_PERMISSION_MM_METER_UNIQUEID_EDIT = "mm_meter_uniqueid_edit";
    public static final String EDIT_PERMISSION_MM_GROUP_ENTITY = "mm_group_entity_edit";
    public static final String EDIT_PERMISSION_MM_ONLINE_BULK = "mm_meter_online_bulk_edit";            //Allow add / edit meters on meter online bulk page
    public static final String EDIT_PERMISSION_MM_USAGE_POINT_BLOCK = "mm_usage_point_block_edit_block";
    public static final String EDIT_PERMISSION_MM_USAGE_POINT_UNBLOCK = "mm_usage_point_block_edit_unblock";
    public static final String EDIT_PERMISSION_MM_METER_MANUFACTURER = "mm_meter_manufacturer_edit";
    public static final String EDIT_PERMISSION_MM_METER_MODEL = "mm_meter_model_edit";
    public static final String EDIT_PERMISSION_MM_SUPPLY_GROUP = "mm_supply_group_edit";
    public static final String EDIT_PERMISSION_MM_BLOCKING_TYPE = "mm_blocking_type_edit";
    public static final String EDIT_PERMISSION_MM_MDC = "mm_mdc_edit";
    public static final String EDIT_PERMISSION_MM_AUX_SCHEDULE = "mm_aux_schedule_edit";
    public static final String EDIT_PERMISSION_MM_AUX_TYPE = "mm_aux_type_edit";
    public static final String EDIT_PERMISSION_MM_PRICING_STRUCT = "mm_pricing_struct_edit";
    public static final String EDIT_PERMISSION_MM_METER_STORE = "mm_meter_store_edit";

    /** Action Permissions */
    public static final String ACTION_PERMISSION_MM_CUST_ACC_SYNC_BALANCE = "mm_cust_acc_sync_balance";
    public static final String ACTION_PERMISSION_MM_UNITS_ACC_SYNC_BALANCE = "mm_units_acc_sync_balance";
    public static final String ACTION_PERMISSION_MM_CUST_ASSIGN = "mm_cust_assign";
    public static final String ACTION_PERMISSION_MM_UP_CALC_TARIFF = "mm_up_calc_tariff";
    public static final String ACTION_PERMISSION_MM_UP_REPLACE_METER = "mm_up_replace_meter";
    public static final String ACTION_PERMISSION_MM_CUST_ACC_THRESHOLDS_ADMIN = "mm_cust_acc_thresholds_admin";
    public static final String ACTION_PERMISSION_MM_NDP_GROUP_ADMIN = "mm_ndp_group_admin";

    /** View Only Permissions */
    public static final String VIEW_ONLY_MM_NDP = "mm_ndp_view";                        //Permission to only view Global & individual NDP times
    public static final String VIEW_ONLY_MM_AUX_ACCOUNTS = "mm_aux_account_view"; // Readonly permission for aux accounts.

    /** Admin Permissions */
    public static final String ADMIN_PERMISSION_MM_AUX_ACCOUNT = "mm_aux_account_admin";
    public static final String ADMIN_PERMISSION_APP_SETTING_VEND_UNITS = "mm_app_setting_vend_units";
    public static final String ADMIN_PERMISSION_VEND_REVERSALS = "mm_do_vend_reversals";
    public static final String ADMIN_PERMISSION_VEND_REPRINT = "mm_do_vend_reprint";
    public static final String ADMIN_PERMISSION_VEND_VERIFYTOKEN = "mm_do_vend_verifytoken";
    public static final String ADMIN_PERMISSION_MM_AUX_DEBT = "mm_aux_debt_admin";
    public static final String ADMIN_PERMISSION_MM_AUX_REFUND = "mm_aux_refund_admin";
    public static final String ADMIN_PERMISSION_MM_STORE_MOVEMENT_DIRECT = "mm_bulk_store_movement_direct";
    public static final String ADMIN_PERMISSION_MM_AUX_ADJUST_DEBT = "mm_aux_debt_adjust_admin";
    public static final String ADMIN_PERMISSION_MM_AUX_ADJUST_REFUND = "mm_aux_refund_adjust_admin";

    public static final String ACCESS_GROUP_TYPE = "access";
    public static final String LOCATION_GROUP_TYPE = "location";
    public static final String USAGE_POINT_GROUP_TYPE = "usagepoint2";
    public static final String SELECTION_DATA_WIDGET_CONTEXT_USAGE_POINT_GROUP = "usagepointgroup";

    public static final int ANIMATION_TIME = 500;
    public static final int DEFAULT_PAGE_SIZE = 10;
    public static final int REPORT_PAGE_SIZE = 15;

    public static final String MAIN_POPUP_STYLE = "mainPopup";

    public static final String SEARCH_STYLE = "searchForm";        

    public static final String CUSTOMER_ID_SEARCH = "customer.id";
    public static final String CUSTOMER_SURNAME_SEARCH = "customer.surname";
    public static final String CUSTOMER_NAME_SEARCH = "customer.name";
    public static final String CUSTOMER_ID_NUMBER_SEARCH = "customer.idnumber";
    public static final String CUSTOMER_TITLE_SEARCH = "customer.title";
    public static final String CUSTOMER_SEARCH_TYPE = "customer.search.type";

    public static final String CUSTOMER_AGREEMENT_SEARCH = "customer.agreement"; 
    public static final String CUSTOMER_AGREEMENT_SEARCH_TYPE = "custom.agreement.search.type";
    public static final String CUSTOMER_AGREEMENT_ID_SEARCH = "customer.agreement.id";
    public static final String CUSTOMER_NO_USAGE_POINT_SEARCH = "customer.no.usage.point";

    public static final String ACCOUNT_NAME_SEARCH = "customer.account.name"; 
    public static final String ACCOUNT_NAME_SEARCH_TYPE = "custom.account.search.type";

    public static final String CUSTOMER_PHONE_NUMBER_SEARCH = "search.customer.phone.number";

    public static final String LOCATION_ERF_NUMBER_SEARCH = "search.location.erf.number";
    public static final String LOCATION_BUILDING_NAME_SEARCH = "search.location.building.name";
    public static final String LOCATION_SUITE_NUMBER_SEARCH = "search.location.suite.number";
    public static final String LOCATION_ADDRESS_1_SEARCH = "search.location.address1";
    public static final String LOCATION_ADDRESS_2_SEARCH = "search.location.address2";
    public static final String LOCATION_ADDRESS_3_SEARCH = "search.location.address3";
    public static final String LOCATION_GROUP_SEARCH = "search.location.group";
    public static final String LOCATION_SEARCH_TYPE = "search.location.type";
    public static final String LOCATION_TYPE = "location.type";

    public static final String METER_ID_SEARCH = "meter.id";    
    public static final String METER_NUMBER_SEARCH = "meter.number";  
    public static final String METER_MODEL_ID_SEARCH = "meter.model.id.search";
    public static final String METER_MODEL_NAME = "meter.model.name";
    public static final String METER_NUMBER_SEARCH_TYPE = "meter.search.type";  
    public static final String METER_NO_USAGE_POINT_SEARCH = "meter.no.usage.point";
    public static final String METER_STORE_SEARCH = "meter.store.id.search";
    public static final String METER_SGC_KRN_SEARCH = "meter.supplygroup.id.search";

    public static final String USAGE_POINT_ID_SEARCH = "usagepoint.id";
    public static final String USAGE_POINT_NAME_SEARCH = "usagepoint.name";
    public static final String USAGE_POINT_NAME_SEARCH_TYPE = "usagepoint.search.type";
    public static final String USAGE_POINT_NO_CUSTOMER_SEARCH = "usage.point.no.customer";
    public static final String USAGE_POINT_NO_METER_SEARCH = "usage.point.no.meter";
    public static final String USAGE_POINT_CUSTOM_VARCHAR1 = "usagepoint.custom.varchar1";
    public static final String USAGE_POINT_CUSTOM_VARCHAR1_STATUS = "usagepoint.custom_varchar1.status";
    public static final String USAGE_POINT_CUSTOM_VARCHAR1_LABEL = "usagepoint.custom_varchar1.label";

    public static final String PRICING_STRUCTURE_ID_SEARCH = "pricing.structure.id";
    public static final String PRICING_STRUCTURE_NAME_SEARCH = "pricing.structure.name";
    public static final String PAYMENT_MODE_ID_SEARCH = "payment.mode.id";
    public static final String PAYMENT_MODE_NAME_SEARCH = "payment.mode.name";

    public static final String MULTI_USAGE_POINT_ENABLE = "multi_usage_point.enable";

    public static final String PLACE_TOKEN_SEPARATOR = "|";   
    public static final String PLACE_TOKEN_SEPARATOR_REG_EXP = "\\|";
    public static final String URL_DATE_TIME_PICKER_FORMAT = "dd-MM-yyyy-HH-mm";  
    public static final String METER_READING_FORMAT = "#0.00";    

    public static final long ONE_HOUR = 1000 * 60 * 60;
    public static final long ONE_DAY = 24 * ONE_HOUR;    

    public static final BigDecimal DEFAULT_ENERGY_BALANCE_VARIATION = new BigDecimal("2.0");

    public static final Long ARM_METER_TYPE_ID = Long.valueOf(2);

    public static final String WILDCARD = "%";
    public static final String PERCENT_SIGN = "%";
    public static final String CONTENTS_FIELD_SEPARATOR = ",";

    public static final String SINGLE_METER_GRAPH_TYPE = "singleMeter";
    public static final String ENERGY_BALANCING_GRAPH_TYPE = "energyBalancing";

    public static final String ENERGY_FORWARD_METER_READING_TYPE = "0.0.0.0.1.0.12.0.0.0.0.0.0.0.0.0.72.0";
    public static final String POWER_DEMAND_METER_READING_TYPE = "0.0.0.0.1.0.8.0.0.0.0.0.0.0.0.0.38.0";    
    public static final String ENERGY_FORWARD_METER_READING_TYPE_WILDCARD = "%.%.%.%.%.%.12.%.%.%.%.%.%.%.%.%.%";
    public static final String POWER_DEMAND_METER_READING_TYPE_WILDCARD = "%.%.%.%.%.%.8.%.%.%.%.%.%.%.%.%.%";
    public static final String POTABLE_WATER_VOLUME_METER_READING_TYPE = "0.0.0.0.1.9.58.0.0.0.0.0.0.0.0.0.134.0";
    public static final String POTABLE_WATER_VOLUME_METER_READING_TYPE_WILDCARD = "%.%.%.%.%.%.58.%.%.%.%.%.%.%.%.%.%";
    public static final String GAS_VOLUME_METER_READING_TYPE = "0.0.0.0.1.7.58.0.0.0.0.0.0.0.0.0.134.0";
    public static final String GAS_VOLUME_METER_READING_TYPE_WILDCARD = "%.%.%.%.1.7.58.%.%.%.%.%.%.%.%.%.134.%";

    public static final String MANUFACTURER_DATA = "manufacturer";
    public static final String MDC_DATA = "mdc";
    public static final String CUSTOMER_ACCOUNT_BALANCE_ADJUSTED = "customerBalance";
    public static final String AUX_ACCOUNT_BALANCE_ADJUSTED = "auxAccountBalance";
    //public static final String PRICING_STRUCTURE_ADDED = "pricingStructureAdded";
    public static final String APPSETTINGS_MODIFIED = "appSettingsModified";
    public static final String BILLING_DET_MODIFIED = "billingDetModified";
    public static final String METER_MODEL_MODIFIED = "meterModel";
    public static final String BLOCKINGTYPES_MODIFIED = "blockingTypesModified";
    public static final String METER_CUST_UP_MODIFIED = "meterCustUpModified";
    public static final String USAGE_POINT_METER_REMOVED = "usagepointMeterRemoved";
    public static final String MODEL_CHANNEL_CONFIG_MODIFIED = "modelchannelconfig";
    public static final String MDC_CHANNEL_MODIFIED = "mdcchannel";
    public static final String UNITS_ACCOUNT_BALANCE_ADJUSTED = "unitsAccountBalance";

    public static final String ONLINE_BULK_METER_ADDED = "meteractivated";
    public static final String ONLINE_BULK_MODIFIED = "onlineBulkModified";
    public static final String USER_INTERFACE_CONFIGURATION_MODIFIED = "userInterfaceConfigurationModified";

    public static final String RELOAD_USAGE_POINT_WORKSPACEVIEW = "reloadUPW";

    public static final int ALLOWED_BLOCKS = 8;
    public static final int PRECISION_DEFAULT = 2;
    public static final int PRECISION_UNITS_STS = 1;
    public static final String ELEC_KWH = "kWh";
    public static final String WATER_KL = "kl";
    public static final String GAS_M3 = "m³";

    public static final String DAILY_SCHEDULE_VALUE = "daily";
    public static final String WEEKLY_SCHEDULE_VALUE = "weekly";
    public static final String MONTHLY_SCHEDULE_VALUE = "monthly";
    public static final String REPEATEDLY_SCHEDULE_VALUE = "repeatedly";

    //Static data in the db scripts so can use their ID here
    public static final Long METER_READINGS_EXPORT_TASK_ID = Long.valueOf(1);
    public static final Long ENERGY_BALANCING_EXPORT_TASK_ID = Long.valueOf(2);
    public static final Long ENERGY_BALANCING_VARIANCE_TASK_ID = Long.valueOf(3);

    public static final String PREVIOUS_DAY_VALUE = "PREVIOUS_DAY";
    public static final String PREVIOUS_WEEK_VALUE = "PREVIOUS_WEEK";
    public static final String PREVIOUS_MONTH_VALUE = "PREVIOUS_MONTH";

    public static final String[] CRON_DAYS = new String[]{"SUN", "MON", "TUE", "WED", "THU", "FRI","SAT"};
    public static final String MINUTES_VALUE = "minutes";
    public static final String HOURS_VALUE = "hours";

    public static final String BULLET_POINT = "&#149; ";
    public static final String ERROR_MESSAGE_STYLE = "errorMessage";

    public static final String NOTIFY_EMAIL_TYPE = "email";
    public static final String NOTIFY_SMS_TYPE = "sms";

    public static final String APP_SETTING_FROM_EMAIL_KEY = "app.notify.email.from.address";
    public static final String APP_SETTING_FROM_NAME_KEY = "app.notify.email.from.name";
    public static final String APP_SETTING_PRICING_STRUCTURE_DISPLAY_SUGGESTION_BOXES = "pricingstructure.displaysuggestionboxes";
    public static final String APP_SETTING_REPRINT_UTILITY_NAME = "reprint.utility_name";
    public static final String APP_SETTING_REPRINT_UTILITY_ADDRESS = "reprint.utility_address";
    public static final String APP_SETTING_REPRINT_UTILITY_CONTACT = "reprint.utility_contact";
    public static final String APP_SETTING_REPRINT_UTILITY_TAX_REF = "reprint.utility_tax_ref";
    public static final String APP_SETTING_REPRINT_UTILITY_DIST_ID = "reprint.utility_dist_id";

    public static final String APP_SETTING_ENG_TOKEN_USER_REF = "engineering.token.issue.user.reference.status";

    public static final String DASHBOARD_ADMIN_PANEL_LIST = "dashboard.admin.panel.list";
    public static final String DASHBOARD_PANEL_LIST = "dashboard.panel.list";
    public static final String DASHBOARD_UPGROUP_ADDED_GROUPTYPE = "dashboard.upgroup.added.grouptype";

    public static final String APP_SETTING_SEARCH_BY_METER_NUMBER_AUTOMATICALLY = "search.search_by_meter_number_automatically";
    public static final String APP_SETTING_SEARCH_BY_METER_NUMBER_CHARACTER_THRESHOLD = "search.search_by_meter_number_character_threshold";

    // Upload Types
    public static final String METER_UPLOAD = "meter";
    public static final String METADATA_UPLOAD = "metadata";
    public static final String METER_CUST_UP_UPLOAD = "metercustup";
    public static final String AUX_ACCOUNT_UPLOAD = "auxaccount";
    public static final String AUX_TRANSACTION_UPLOAD = "auxtransaction";
    public static final String CUST_TRANSACTION_UPLOAD = "custtransaction";

    public static final String AUX_ACCOUNT_HISTORY_UPDATE = "auxaccounthist";

    public static final String VEND_UNITS_AMOUNT_PATTERN = "vend.units.amount.%";

    public static final String MAP_READY = "map_ready";

    public static final String FILETYPE_SAP_DEBT_IMPORT = "za.co.ipay.metermng.integration.sap.debtimport.DebtImportHandler";  //SAP Debt Import";
    public static final String FILETYPE_SAP_CUSTOMER_MOVEMENTS = "za.co.ipay.metermng.integration.sap.customermovement.CustomerMovementFileImportHandler";  //SAP Customer Movements";
    public static final String FILETYPE_SAMRAS_DEBT_IMPORT = "za.co.ipay.metermng.integration.samras.debtimport.SamrasDebtImportHandler";  //Samras Debt Import";
    public static final String FILETYPE_SOLAR_DEBT_IMPORT = "za.co.ipay.metermng.integration.solar.debtimport.SolarDebtImportHandler";  //SOLAR Debt Import";
    public static final String FILETYPE_METERCUSTUP_BULK_IMPORT = "za.co.ipay.metermng.integration.offlinebulkuploads.metercustup.MeterCustUpBulkImportHandler";  //MeterCustUp Bulk Import";
    public static final String FILETYPE_REGISTER_READING_IMPORT = "za.co.ipay.metermng.integration.registerreading.RegisterReadingImportHandler";  //Register Reading Import";
    public static final String FILETYPE_BULK_TARIFF_UPDATER = "za.co.ipay.metermng.integration.TariffBulkUpload.TariffPsImportHandler";  //Bulk Tariff Updater";
    public static final String FILETYPE_BULK_KEY_CHANGE_GENERATOR = "za.co.ipay.metermng.integration.bulkKeychange.BulkKeyChangeImportHandler";  //Bulk Key Change Generator";
    public static final String FILETYPE_BULK_PRICING_STRUCTURE_CHANGE_GENERATOR = "za.co.ipay.metermng.integration.bulkpricingstructurechange.BulkPricingStructureChangeImportHandler";
    public static final String FILETYPE_BULK_BLOCKING_GENERATOR = "za.co.ipay.metermng.integration.bulkblocking.BulkBlockingImportHandler";
    public static final String FILETYPE_BULK_STORE_MOVEMENT = "za.co.ipay.metermng.integration.bulkstoremovement.BulkStoreMovementImportHandler";
    public static final String FILETYPE_IPAY_DEBT_IMPORT = "za.co.ipay.metermng.integration.ipay.debtimport.IpayDebtImportHandler";
    public static final String FILETYPE_PRICING_STRUCTURE_CHANGE_IMPORT = "za.co.ipay.metermng.integration.pricingstructurechange.PricingStructureChangeImportHandler";
    public static final String FILETYPE_BULK_MDC_IMPORT = "za.co.ipay.metermng.integration.bulkmdccontrol.BulkMdcImportHandler";
    public static final String FILETYPE_BULK_FREE_ISSUE_IMPORT = "za.co.ipay.metermng.integration.bulkfreeissue.BulkFreeIssueImportHandler";


    public static final String APP_SETTING_LOCATION_REQUIRED_TO_ALL_LEVELS = "location.required.to.all.levels";

    public static final int DELETE_EXISTING_READINGS_ALL = 0;
    public static final int DELETE_EXISTING_READINGS_SELECTED = 1;
    public static final int DELETE_EXISTING_READINGS_APPEND = 2;

    public static final String EXPORT_TYPE = "exporttype";
    public static final String EXPORT_FILE_NAME_PREFIX = "filenameprefix";
    public static final String EXPORT_FILTER_VALUE = "filtervalue";    
    public static final String EXPORT_FILTER_ELEMENT = "filterelement";
    public static final String EXPORT_ADVANCED_SEARCH = "advancedsearch";

    // TODO: <Redacted> should be moved to i18n files
    public static final String ACCESSGROUP_RLS_HIDDEN_DATA = "<Redacted>";
    public static final String CUSTOMER_CUSTOMER_TRANS_EXPORT = "customer";
    public static final String AUX_CUSTOMER_CUSTOMER_TRANS_EXPORT = "auxcustomer";

    public static final String ALLOW_REVERSAL_WHEN_REPRINTED = "mm_allow_reversal_when_reprinted";

    private MeterMngStatics() {
        //no instances required
    }

   public static String symbolFromServiceResource(ServiceResourceE serviceResource) {
        switch (serviceResource) {
        case ELEC:
            return ELEC_KWH;
        case WATER:
            return WATER_KL;
        case GAS:
            return GAS_M3;
        default:
            return "";
        }
   }

}
