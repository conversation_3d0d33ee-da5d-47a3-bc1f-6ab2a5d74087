package za.co.ipay.metermng.shared.integration.bulkfreeissue;

import java.io.Serializable;

/**
 * Result of parsing a bulk free issue import record.
 */
public class BulkFreeIssueParseResult implements Serializable {
    private static final long serialVersionUID = 1L;

    private BulkFreeIssueImportRecord bulkFreeIssueImportRecord;
    private String errorMsg;

    public BulkFreeIssueParseResult() {
        // Default constructor required for serialization
    }

    public BulkFreeIssueParseResult(BulkFreeIssueImportRecord bulkFreeIssueImportRecord, String errorMsg) {
        this.bulkFreeIssueImportRecord = bulkFreeIssueImportRecord;
        this.errorMsg = errorMsg;
    }

    public BulkFreeIssueImportRecord getBulkFreeIssueImportRecord() {
        return bulkFreeIssueImportRecord;
    }

    public void setBulkFreeIssueImportRecord(BulkFreeIssueImportRecord bulkFreeIssueImportRecord) {
        this.bulkFreeIssueImportRecord = bulkFreeIssueImportRecord;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}