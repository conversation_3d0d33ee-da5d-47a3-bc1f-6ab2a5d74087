package za.co.ipay.metermng.shared.integration.bulkfreeissue;

import java.io.Serializable;

import za.co.ipay.metermng.shared.integration.GenericImportRecord;

/**
 * Data model for bulk free issue import records.
 */
public class BulkFreeIssueImportRecord implements GenericImportRecord {
    private static final long serialVersionUID = 1L;

    private String meterNumber;
    private String units;
    private String description;
    private String reference;
    private String reason;

    public BulkFreeIssueImportRecord() {
        // Default constructor required for serialization
    }

    public BulkFreeIssueImportRecord(String meterNumber, String units, String description, String reference, String reason) {
        this.meterNumber = meterNumber;
        this.units = units;
        this.description = description;
        this.reference = reference;
        this.reason = reason;
    }

    public String getMeterNumber() {
        return meterNumber;
    }

    public void setMeterNumber(String meterNumber) {
        this.meterNumber = meterNumber;
    }

    public String getUnits() {
        return units;
    }

    public void setUnits(String units) {
        this.units = units;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
